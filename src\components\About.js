import React from 'react';
import { motion } from 'framer-motion';
import { FaCode, FaLaptopCode, FaMobile, FaServer } from 'react-icons/fa';
import './About.css';

const About = () => {
  const services = [
    {
      icon: <FaCode />,
      title: 'Frontend Development',
      description: 'Creating responsive and interactive user interfaces using React, Vue, and modern CSS frameworks.'
    },
    {
      icon: <FaServer />,
      title: 'Backend Development',
      description: 'Building robust server-side applications with Node.js, Python, and database management.'
    },
    {
      icon: <FaMobile />,
      title: 'Mobile Development',
      description: 'Developing cross-platform mobile applications using React Native and Flutter.'
    },
    {
      icon: <FaLaptopCode />,
      title: 'Full Stack Solutions',
      description: 'End-to-end web application development from concept to deployment.'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section id="about" className="about">
      <div className="about-container">
        <motion.div 
          className="about-content"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.3 }}
        >
          <motion.div variants={itemVariants} className="section-header">
            <h2 className="section-title">About Me</h2>
            <p className="section-subtitle">Get to know me better</p>
          </motion.div>

          <div className="about-main">
            <motion.div variants={itemVariants} className="about-text">
              <h3>Hello! I'm a passionate developer</h3>
              <p>
                I'm a full-stack developer with over 3 years of experience in creating 
                digital experiences. I specialize in JavaScript technologies and have a 
                strong background in both frontend and backend development.
              </p>
              <p>
                My journey in web development started with curiosity about how websites work, 
                and it has evolved into a passion for creating efficient, scalable, and 
                user-friendly applications. I love learning new technologies and staying 
                up-to-date with the latest industry trends.
              </p>
              <p>
                When I'm not coding, you can find me exploring new technologies, contributing 
                to open-source projects, or sharing knowledge with the developer community.
              </p>
              
              <div className="stats">
                <div className="stat">
                  <h4>50+</h4>
                  <p>Projects Completed</p>
                </div>
                <div className="stat">
                  <h4>3+</h4>
                  <p>Years Experience</p>
                </div>
                <div className="stat">
                  <h4>20+</h4>
                  <p>Happy Clients</p>
                </div>
              </div>
            </motion.div>

            <motion.div variants={itemVariants} className="about-image">
              <div className="image-placeholder">
                <span>Professional Photo</span>
              </div>
            </motion.div>
          </div>

          <motion.div variants={itemVariants} className="services">
            <h3>What I Do</h3>
            <div className="services-grid">
              {services.map((service, index) => (
                <motion.div 
                  key={index}
                  className="service-card"
                  whileHover={{ y: -5 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="service-icon">
                    {service.icon}
                  </div>
                  <h4>{service.title}</h4>
                  <p>{service.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
