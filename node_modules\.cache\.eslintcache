[{"C:\\Latihan_React\\my-react-app\\src\\index.js": "1", "C:\\Latihan_React\\my-react-app\\src\\App.js": "2", "C:\\Latihan_React\\my-react-app\\src\\reportWebVitals.js": "3", "C:\\Latihan_React\\my-react-app\\src\\components\\Hero.js": "4", "C:\\Latihan_React\\my-react-app\\src\\components\\About.js": "5", "C:\\Latihan_React\\my-react-app\\src\\components\\Header.js": "6", "C:\\Latihan_React\\my-react-app\\src\\components\\Skills.js": "7", "C:\\Latihan_React\\my-react-app\\src\\components\\Projects.js": "8", "C:\\Latihan_React\\my-react-app\\src\\components\\Contact.js": "9"}, {"size": 535, "mtime": 1753364644036, "results": "10", "hashOfConfig": "11"}, {"size": 494, "mtime": 1753367991403, "results": "12", "hashOfConfig": "11"}, {"size": 362, "mtime": 1753364644039, "results": "13", "hashOfConfig": "11"}, {"size": 4919, "mtime": 1753945685562, "results": "14", "hashOfConfig": "11"}, {"size": 4385, "mtime": 1753367751627, "results": "15", "hashOfConfig": "11"}, {"size": 2073, "mtime": 1753367683207, "results": "16", "hashOfConfig": "11"}, {"size": 4278, "mtime": 1753367793545, "results": "17", "hashOfConfig": "11"}, {"size": 3101, "mtime": 1753367936965, "results": "18", "hashOfConfig": "11"}, {"size": 3683, "mtime": 1753367967464, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "q176jb", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Latihan_React\\my-react-app\\src\\index.js", [], [], "C:\\Latihan_React\\my-react-app\\src\\App.js", [], [], "C:\\<PERSON><PERSON>han_React\\my-react-app\\src\\reportWebVitals.js", [], [], "C:\\Latihan_React\\my-react-app\\src\\components\\Hero.js", [], [], "C:\\Latihan_React\\my-react-app\\src\\components\\About.js", [], [], "C:\\Latihan_React\\my-react-app\\src\\components\\Header.js", [], [], "C:\\Latihan_React\\my-react-app\\src\\components\\Skills.js", [], [], "C:\\Latihan_React\\my-react-app\\src\\components\\Projects.js", [], [], "C:\\Latihan_React\\my-react-app\\src\\components\\Contact.js", [], []]